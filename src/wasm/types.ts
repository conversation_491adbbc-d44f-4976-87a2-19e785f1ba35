export type u32 = bigint & { __type: "u32" };
export const u32 = (num: number | bigint) => {
  return BigInt.asUintN(32, BigInt(num));
}

export type u8 = bigint & { __type: "u8" };
export const u8 = (num: number | bigint) => {
  return BigInt.asUintN(8, BigInt(num));
}

export type s33 = bigint & { __type: "s33" };
export const s33 = (num: number | bigint) => {
  return BigInt.asIntN(33, BigInt(num));
}

export type NumType = u32 & { __subtype: "NumType" };
export const I32_NUM_TYPE = u32(0x7F) as NumType;
export const I64_NUM_TYPE = u32(0x7E) as NumType;
export const F32_NUM_TYPE = u32(0x7D) as NumType;
export const F64_NUM_TYPE = u32(0x7C) as NumType;

export const i32_nt = I32_NUM_TYPE;
export const i64_nt = I64_NUM_TYPE;
export const f32_nt = F32_NUM_TYPE;
export const f64_nt = F64_NUM_TYPE;

export type Limits = {
  type: "Limits",
  min: u32,
  max?: u32,
};

export const limits = (min: u32, max: u32 | undefined = undefined) => {
  return { type: "Limits", min, max }; 
};

export type Vector<T> = [] & { __type: "Vector", __subtype: T };
export const vector = <T>(val: T[]) => val as Vector<T>;

export type Name = Vector<u8>;

export const VecType = 0x7B;

export type AbstractHeapType = s33 & { __subtype: "AbstractHeapType" };
export const NO_EXN_ABSTRACT_HEAP_TYPE = s33(0x74) as AbstractHeapType;
export const NO_FUNC_ABSTRACT_HEAP_TYPE = s33(0x73) as AbstractHeapType;
export const NO_EXTERN_ABSTRACT_HEAP_TYPE = s33(0x72) as AbstractHeapType;
export const NONE_ABSTRACT_HEAP_TYPE = s33(0x71) as AbstractHeapType;
export const FUNC_ABSTRACT_HEAP_TYPE = s33(0x70) as AbstractHeapType;
export const EXTERN_ABSTRACT_HEAP_TYPE = s33(0x6F) as AbstractHeapType;
export const ANY_ABSTRACT_HEAP_TYPE = s33(0x6E) as AbstractHeapType;
export const EQ_ABSTRACT_HEAP_TYPE = s33(0x6D) as AbstractHeapType;
export const I31_ABSTRACT_HEAP_TYPE = s33(0x6C) as AbstractHeapType;
export const STRUCT_ABSTRACT_HEAP_TYPE = s33(0x6B) as AbstractHeapType;
export const ARRAY_ABSTRACT_HEAP_TYPE = s33(0x6A) as AbstractHeapType;
export const EXN_ABSTRACT_HEAP_TYPE = s33(0x69) as AbstractHeapType;
export type HeapType =
  | { type: "HeapType", subtype: "AbstractHeapType", value: AbstractHeapType }
  | { type: "HeapType", subtype: "HeapTypeIndex", index: s33 };

export const heap_type = (value: AbstractHeapType): HeapType => ({
  type: "HeapType",
  subtype: "AbstractHeapType",
  value,
});

export const heap_type_index = (index: s33): HeapType => ({
  type: "HeapType",
  subtype: "HeapTypeIndex",
  index,
});

export type RefType =
  | { type: "RefType", ht: HeapType, nullable: boolean };

export const ref_type = (ht: HeapType, nullable = false) => ({
  type: "RefType",
  ht,
  nullable,
});

export const noexn_aht = NO_EXN_ABSTRACT_HEAP_TYPE;
export const nofunc_aht = NO_FUNC_ABSTRACT_HEAP_TYPE;
export const no_extern_aht = NO_EXTERN_ABSTRACT_HEAP_TYPE;
export const none_aht = NONE_ABSTRACT_HEAP_TYPE;
export const func_aht = FUNC_ABSTRACT_HEAP_TYPE;
export const extern_aht = EXTERN_ABSTRACT_HEAP_TYPE;
export const any_aht = ANY_ABSTRACT_HEAP_TYPE;
export const eq_aht = EQ_ABSTRACT_HEAP_TYPE;
export const i31_aht = I31_ABSTRACT_HEAP_TYPE;
export const struct_aht = STRUCT_ABSTRACT_HEAP_TYPE;
export const array_aht = ARRAY_ABSTRACT_HEAP_TYPE;
export const exn_aht = EXN_ABSTRACT_HEAP_TYPE;

export const noexn_ht = heap_type(noexn_aht);
export const nofunc_ht = heap_type(nofunc_aht);
export const no_extern_ht = heap_type(no_extern_aht);
export const none_ht = heap_type(none_aht);
export const func_ht = heap_type(func_aht);
export const extern_ht = heap_type(extern_aht);
export const any_ht = heap_type(any_aht);
export const eq_ht = heap_type(eq_aht);
export const i31_ht = heap_type(i31_aht);
export const struct_ht = heap_type(struct_aht);
export const array_ht = heap_type(array_aht);
export const exn_ht = heap_type(exn_aht);

export const noexn_rt = ref_type(noexn_ht, true);
export const nofunc_rt = ref_type(nofunc_ht, true);
export const no_extern_rt = ref_type(no_extern_ht, true);
export const none_rt = ref_type(none_ht, true);
export const func_rt = ref_type(func_ht, true);
export const extern_rt = ref_type(extern_ht, true);
export const any_rt = ref_type(any_ht, true);
export const eq_rt = ref_type(eq_ht, true);
export const i31_rt = ref_type(i31_ht, true);
export const struct_rt = ref_type(struct_ht, true);
export const array_rt = ref_type(array_ht, true);
export const exn_rt = ref_type(exn_ht, true);

export type ValType =
  | { type: "ValType", subtype: "NumType", nt: NumType }
  | { type: "ValType", subtype: "VecType" }
  | { type: "ValType", subtype: "RefType", rt: RefType };

export const numtype_vt = (nt: NumType): ValType => ({
  type: "ValType",
  subtype: "NumType",
  nt,
});

export const i32_vt = numtype_vt(i32_nt);
export const i64_vt = numtype_vt(i64_nt);