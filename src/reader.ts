import * as fs from "node:fs/promises";

const TOP_FIVE = 0b1111_1000;
const TOP_FOUR = 0b1111_0000;
const TOP_THREE = 0b1110_0000;
const TOP_TWO = 0b1100_0000;
const TOP_ONE = 0b1000_0000;
const TOP_ONE_BIGINT = BigInt(TOP_ONE);

const BOTTOM_SIX = 0b0011_1111;
const BOTTOM_SEVEN = 0b0111_1111;
const BOTTOM_SEVEN_BIGINT = BigInt(BOTTOM_SEVEN);

export type Reader = AsyncGenerator<number | undefined, void, unknown>;

export async function* readBytes(filePath: string) {
  const handle = await fs.open(filePath, "r");
  while (true) {
    const { buffer, bytesRead } = await handle.read();
    if (bytesRead === 0) break;
    for (let i = 0; i < bytesRead; i++) {
      yield buffer[i]!;
    }
  }
}

export async function* fromBytes(values: <PERSON>rrayLike<number>) {
  const count = values.length;
  for (let i = 0; i < count; i++) {
    yield values[i];
  }
}

export async function take(bytes: Reader, count: number, signed = false) {
  const is_negative = 0b1000_0000 << ((count - 1) * 8);
  let acc = 0;
  for (let i = 0; i < count; i++) {
    const { value, done } = await bytes.next();
    if (done) return null;
    acc = (acc << 8) | value!;
  }

  return signed ? (acc & is_negative ? -(~acc + 1) : acc) : acc;
}

export async function take_bigint(bytes: Reader, count: number, signed = false) {
  let acc = 0n;
  for (let i = 0; i < count; i++) {
    const { value, done } = await bytes.next();
    if (done) return null;
    acc = (acc << 8n) | BigInt(value!);
  }

  return signed ? BigInt.asIntN(count * 8, acc) : acc;
}

export const u8 = async (bytes: Reader) => {
  const { value, done } = await bytes.next();
  return done ? null : value!;
}
export const u16 = (bytes: Reader) => take(bytes, 2);
export const u32 = (bytes: Reader) => take(bytes, 4);
export const u64 = (bytes: Reader) => take(bytes, 8);

export const i8 = (bytes: Reader) => take(bytes, 1, true);
export const i16 = (bytes: Reader) => take(bytes, 2, true);
export const i32 = (bytes: Reader) => take(bytes, 4, true);
export const i64 = (bytes: Reader) => take(bytes, 8, true);

export const rune = async (bytes: Reader) => {
  const byte = await take(bytes, 1);
  if (byte === null || byte < 0x80n) return byte;

  const is_four = (byte & TOP_FIVE) === TOP_FOUR;
  const is_three = (byte & TOP_FOUR) === TOP_THREE;
  const is_two = (byte & TOP_THREE) === TOP_TWO;

  const extra_bytes =
    (Number(is_four) * 3) | (Number(is_three) * 2) | (Number(is_two) * 1);
  // Extrabytes = 1 -> 0b00111111
  // Extrabytes = 2 -> 0b00011111
  // Extrabytes = 3 -> 0b00001111
  const top_bits = byte & (BOTTOM_SEVEN >> Number(extra_bytes));


  const rest = await take(bytes, extra_bytes);
  if (rest === null) return null;

  const byte_two = rest >> (8 * (Number(extra_bytes) - 1));
  const byte_three = (Number(is_four) | Number(is_three)) * (0xff & (rest >> (Number(is_four) * 8)));
  const byte_four = Number(is_four) * 0xff & rest;

  const all_valid =
    Number((byte_two & TOP_TWO) === TOP_ONE)
    & (Number(extra_bytes == 1) | Number((byte_three & TOP_TWO) === TOP_ONE))
    & (Number(extra_bytes < 3) | Number((byte_four & TOP_TWO) === TOP_ONE));
    
  return (
    // if everything is valid, then the bitwise or mask is 0 otherwise it's -1.
    // (valid)   | == 1 | 0
    // (invalid) | == 0 | -1
    (all_valid - 1)
    // first byte
    | (top_bits << (6 * extra_bytes))
    // second byte
    | ((byte_two & BOTTOM_SIX) << (6 * (extra_bytes - 1)))
    // third byte
    | (Number(extra_bytes >= 2) * ((byte_three & BOTTOM_SIX) << (6 * (extra_bytes - 2))))
    // fourth byte
    | (Number(extra_bytes == 3) * (byte_four & BOTTOM_SIX))
  );
};

export const leb128_u = async (bytes: Reader, size = 32) => {
  let result = 0n;
  let shift = 0n;
  const all_mask = (1n << BigInt(size)) - 1n;

  do {
    const byte = await take_bigint(bytes, 1);
    if (byte === null) return null;

    if (byte >= 0x80n && size >= 7) {
      result |= (byte & 0x7fn) << shift; /* low-order 7 bits of value */
      shift += 7n;
      size -= 7;
      continue;
    }

    if (byte >= 0x80n) return null;
    result |= (byte & 0x7fn) << shift;

    

    return result & all_mask;
  } while (true);
}


export const leb128_s = async (bytes: Reader, size = 32) => {
  const sign_bit = 0b1n << BigInt(size - 1);
  const value_mask = sign_bit - 1n;
  const all_mask = sign_bit | value_mask;

  let result = 0n;
  let shift = 0n;
  while (true) {
    const byte = await take_bigint(bytes, 1);
    if (byte === null) return null;
    
    if (byte >= 0x80n && size >= 7) {
      result |= (byte & 0x7fn) << shift;
      shift += 7n;
      size -= 7;
      continue;
    }

    if (byte >= 0x80n) return null;
    result |= (byte & 0x7fn) << shift;
    result &= all_mask;
    
    return sign_bit & result ? (result & value_mask) - sign_bit : result;
  }
}
